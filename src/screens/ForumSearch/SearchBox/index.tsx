/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import type React from 'react';
import { useState } from 'react';
import { TextInput, View, Text, Pressable } from 'react-native';
import BackButton from '@/src/components/BackButton';
import Tabs from '@/src/components/Tabs';
import RecentSearches from '../RecentSearches';
import SearchResults from '../SearchResults';
import type { ForumSearchCategoryTabs } from '../SearchResults/types';
import { forumPostsDummy, communitiesDummy } from './dummy';
import type { ForumSearchCategory, ForumSearchResponse, ForumSearchType } from './types';
import { useNavigation } from '@react-navigation/native';

const tabs: ForumSearchCategoryTabs[] = [
  { id: 'posts', label: 'Questions' },
  { id: 'communities', label: 'Communities' },
];

const Searchbox = (searchType: ForumSearchType = { searchType: 'general' }) => {
  const [searchData, setSearchData] = useState<string>('');
  const [showRecent, setShowRecent] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<ForumSearchCategory>('posts');
  const [searchResults, setSearchResults] = useState<
    Record<ForumSearchCategory, ForumSearchResponse>
  >({
    posts: { data: [], total: 0 },
    communities: { data: [], total: 0 },
  });
  const [lastSearchQuery, setLastSearchQuery] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const navigation = useNavigation()

  const filterPosts = (query: string, category: ForumSearchCategory) => {
    const filtered = forumPostsDummy.filter(
      (post) =>
        post.title.toLowerCase().includes(query.toLowerCase()) ||
        (post.description && post.description.toLowerCase().includes(query.toLowerCase())),
    );
    return filtered;
  };

  const filterCommunities = (query: string, category: ForumSearchCategory) => {
    return communitiesDummy.filter((c) => c.toLowerCase().includes(query.toLowerCase()));
  };

  const handleSubmit = async () => {
    const trimmed = searchData.trim();
    if (trimmed) {
      setLoading(true);
      setLastSearchQuery(trimmed);
      setShowRecent(false);
      setSearchResults({
        posts: {
          data: filterPosts(trimmed, 'posts'),
          total: filterPosts(trimmed, 'posts').length,
        },
        communities: {
          data: filterCommunities(trimmed, 'communities'),
          total: filterCommunities(trimmed, 'communities').length,
        },
      });
      setLoading(false);
    }
  };

  const handleTabChange = (tab: ForumSearchCategory) => {
    setActiveTab(tab);
    if (lastSearchQuery.trim()) {
      setSearchResults((prev) => ({
        ...prev,
        [tab]: {
          data:
            tab === 'posts'
              ? filterPosts(lastSearchQuery, tab)
              : filterCommunities(lastSearchQuery, tab),
          total:
            tab === 'posts'
              ? filterPosts(lastSearchQuery, tab).length
              : filterCommunities(lastSearchQuery, tab).length,
        },
      }));
    }
  };

  const handleSearchChange = (text: string) => {
    setSearchData(text);
    if (text.trim() === '') {
      setSearchResults({
        posts: { data: [], total: 0 },
        communities: { data: [], total: 0 },
      });
      setShowRecent(true);
      setLastSearchQuery('');
    }
  };

  const handleLoadMore = () => {};
  const handleRefresh = () => {};

  return (
    <View className="flex-1 bg-white">
      <View className="px-4">
        <View className="flex-row items-center space-x-3">
          <BackButton onBack={() => navigation.goBack()} label="" />
          <View className="flex-1 flex-row items-center bg-gray-100 rounded-xl px-3">
            <TextInput
              autoFocus
              placeholder="Search..."
              placeholderTextColor="#6b7280"
              value={searchData}
              onChangeText={handleSearchChange}
              onSubmitEditing={handleSubmit}
              returnKeyType="search"
              className="flex-1 text-black py-3"
            />

            {searchData.length > 0 && (
              <Pressable
                onPress={() => {
                  setSearchData('');
                  setSearchResults({
                    posts: { data: [], total: 0 },
                    communities: { data: [], total: 0 },
                  });
                  setShowRecent(true);
                  setLastSearchQuery('');
                }}
                className="p-1"
              >
                <Text className="text-gray-500">✕</Text>
              </Pressable>
            )}
          </View>
        </View>
      </View>
      <View className="flex-1">
        {showRecent ? (
          <RecentSearches
            setSearchData={setSearchData}
            setActiveTab={setActiveTab}
            setLoading={setLoading}
            setShowRecent={setShowRecent}
            setLastSearchQuery={setLastSearchQuery}
          />
        ) : (
          <>
            {searchType.searchType === 'general' && (
              <View className="pt-4">
                <Tabs
                  tabs={tabs}
                  activeTab={activeTab}
                  onTabChange={(tab) => {
                    handleTabChange(tab as ForumSearchCategory);
                  }}
                  disabled={loading}
                />
              </View>
            )}
            <SearchResults
              activeTab={activeTab}
              searchResults={searchResults[activeTab]}
              loading={loading}
              refreshing={refreshing}
              onRefresh={handleRefresh}
              searchType={searchType.searchType}
            />
          </>
        )}
      </View>
    </View>
  );
};

export default Searchbox;
